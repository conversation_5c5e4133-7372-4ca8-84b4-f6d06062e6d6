<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceRequestRecordDetailDAO">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="manageDetailId" column="manage_detail_id" jdbcType="BIGINT"/>
            <result property="requestRecordId" column="request_record_id" jdbcType="BIGINT"/>
            <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
            <result property="productCount" column="product_count" jdbcType="INTEGER"/>
            <result property="amount" column="amount" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="BIGINT"/>
            <result property="gmtUpdate" column="gmt_update" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,manage_detail_id,request_record_id,
        product_code,product_count,
        amount,status,deleted,
        gmt_create,gmt_update
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from invoice_request_record_detail
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from invoice_request_record_detail
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO" useGeneratedKeys="true">
        insert into invoice_request_record_detail
        ( id,manage_id,manage_detail_id,request_record_id
        ,product_code,product_count
        ,amount,status,deleted
        ,gmt_create,gmt_update)
        values (#{id,jdbcType=BIGINT},#{manageId,jdbcType=BIGINT},#{manageDetailId,jdbcType=BIGINT},#{requestRecordId,jdbcType=BIGINT}
        ,#{productCode,jdbcType=VARCHAR},#{productCount,jdbcType=INTEGER}
        ,#{amount,jdbcType=BIGINT},#{status,jdbcType=TINYINT},#{deleted,jdbcType=TINYINT}
        ,#{gmtCreate,jdbcType=BIGINT},#{gmtUpdate,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO" useGeneratedKeys="true">
        insert into invoice_request_record_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="manageId != null">manage_id,</if>
                <if test="manageDetailId != null">manage_detail_id,</if>
                <if test="requestRecordId != null">request_record_id,</if>
                <if test="productCode != null">product_code,</if>
                <if test="productCount != null">product_count,</if>
                <if test="amount != null">amount,</if>
                <if test="status != null">status,</if>
                <if test="deleted != null">deleted,</if>
                <if test="gmtCreate != null">gmt_create,</if>
                <if test="gmtUpdate != null">gmt_update,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="manageId != null">#{manageId,jdbcType=BIGINT},</if>
                <if test="manageDetailId != null">#{manageDetailId,jdbcType=BIGINT},</if>
                <if test="requestRecordId != null">#{requestRecordId,jdbcType=BIGINT},</if>
                <if test="productCode != null">#{productCode,jdbcType=VARCHAR},</if>
                <if test="productCount != null">#{productCount,jdbcType=INTEGER},</if>
                <if test="amount != null">#{amount,jdbcType=BIGINT},</if>
                <if test="status != null">#{status,jdbcType=TINYINT},</if>
                <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
                <if test="gmtCreate != null">#{gmtCreate,jdbcType=BIGINT},</if>
                <if test="gmtUpdate != null">#{gmtUpdate,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO">
        update invoice_request_record_detail
        <set>
                <if test="manageId != null">
                    manage_id = #{manageId,jdbcType=BIGINT},
                </if>
                <if test="manageDetailId != null">
                    manage_detail_id = #{manageDetailId,jdbcType=BIGINT},
                </if>
                <if test="requestRecordId != null">
                    request_record_id = #{requestRecordId,jdbcType=BIGINT},
                </if>
                <if test="productCode != null">
                    product_code = #{productCode,jdbcType=VARCHAR},
                </if>
                <if test="productCount != null">
                    product_count = #{productCount,jdbcType=INTEGER},
                </if>
                <if test="amount != null">
                    amount = #{amount,jdbcType=BIGINT},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=TINYINT},
                </if>
                <if test="deleted != null">
                    deleted = #{deleted,jdbcType=TINYINT},
                </if>
                <if test="gmtCreate != null">
                    gmt_create = #{gmtCreate,jdbcType=BIGINT},
                </if>
                <if test="gmtUpdate != null">
                    gmt_update = #{gmtUpdate,jdbcType=BIGINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO">
        update invoice_request_record_detail
        set
            manage_id =  #{manageId,jdbcType=BIGINT},
            manage_detail_id =  #{manageDetailId,jdbcType=BIGINT},
            request_record_id =  #{requestRecordId,jdbcType=BIGINT},
            product_code =  #{productCode,jdbcType=VARCHAR},
            product_count =  #{productCount,jdbcType=INTEGER},
            amount =  #{amount,jdbcType=BIGINT},
            status =  #{status,jdbcType=TINYINT},
            deleted =  #{deleted,jdbcType=TINYINT},
            gmt_create =  #{gmtCreate,jdbcType=BIGINT},
            gmt_update =  #{gmtUpdate,jdbcType=BIGINT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <update id="updateByRequestRecordId">
        UPDATE invoice_request_record_detail SET status = #{status} WHERE request_record_id = #{requestRecordId}
    </update>

    <select id="listByManageIds" resultType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO">
        SELECT * FROM invoice_request_record_detail
        WHERE deleted = 0 and manage_id IN
        <foreach item="id" index="index" collection="manageIds"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getByManageId" resultType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO">
        SELECT * FROM invoice_request_record_detail WHERE deleted = 0 and manage_id = #{manageId}
    </select>

    <select id="getByRequestRecordId" resultType="so.dian.invoice.pojo.entity.InvoiceRequestRecordDetailDO">
        SELECT * FROM invoice_request_record_detail
        <where>
            deleted = 0
            <if test="requestRecordId != null">
                AND request_record_id = #{requestRecordId}
            </if>
        </where>
    </select>

    <insert id="batchInsert" parameterType="list" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO invoice_request_record_detail
        (manage_id, manage_detail_id, request_record_id, product_code, product_count, amount, status, deleted, gmt_create, gmt_update)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.manageId,jdbcType=BIGINT}, #{item.manageDetailId,jdbcType=BIGINT}, #{item.requestRecordId,jdbcType=BIGINT},
             #{item.productCode,jdbcType=VARCHAR}, #{item.productCount,jdbcType=INTEGER}, #{item.amount,jdbcType=BIGINT},
             #{item.status,jdbcType=TINYINT}, #{item.deleted,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=BIGINT}, #{item.gmtUpdate,jdbcType=BIGINT})
        </foreach>
    </insert>
</mapper>
