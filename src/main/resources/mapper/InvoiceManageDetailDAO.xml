<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.invoice.dao.InvoiceManageDetailDAO">

    <resultMap id="BaseResultMap" type="so.dian.invoice.pojo.entity.InvoiceManageDetailDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="manageId" column="manage_id" jdbcType="BIGINT"/>
            <result property="amount" column="amount" jdbcType="BIGINT"/>
            <result property="originAmount" column="origin_amount" jdbcType="BIGINT"/>
            <result property="expectedInvoiceAmount" column="expected_invoice_amount" jdbcType="BIGINT"/>
            <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
            <result property="productCount" column="product_count" jdbcType="INTEGER"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="BIGINT"/>
            <result property="gmtUpdate" column="gmt_update" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,manage_id,amount,
        origin_amount,expected_invoice_amount,product_code,
        product_count,deleted,
        gmt_create,gmt_update
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from invoice_manage_detail
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from invoice_manage_detail
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceManageDetailDO" useGeneratedKeys="true">
        insert into invoice_manage_detail
        ( id,manage_id,amount
        ,origin_amount,expected_invoice_amount,product_code
        ,product_count,deleted
        ,gmt_create,gmt_update)
        values (#{id,jdbcType=BIGINT},#{manageId,jdbcType=BIGINT},#{amount,jdbcType=BIGINT}
        ,#{originAmount,jdbcType=BIGINT},#{expectedInvoiceAmount,jdbcType=BIGINT},#{productCode,jdbcType=VARCHAR}
        ,#{productCount,jdbcType=INTEGER},#{deleted,jdbcType=TINYINT}
        ,#{gmtCreate,jdbcType=BIGINT},#{gmtUpdate,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.invoice.pojo.entity.InvoiceManageDetailDO" useGeneratedKeys="true">
        insert into invoice_manage_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="manageId != null">manage_id,</if>
                <if test="amount != null">amount,</if>
                <if test="originAmount != null">origin_amount,</if>
                <if test="expectedInvoiceAmount != null">expected_invoice_amount,</if>
                <if test="productCode != null">product_code,</if>
                <if test="productCount != null">product_count,</if>
                <if test="deleted != null">deleted,</if>
                <if test="gmtCreate != null">gmt_create,</if>
                <if test="gmtUpdate != null">gmt_update,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="manageId != null">#{manageId,jdbcType=BIGINT},</if>
                <if test="amount != null">#{amount,jdbcType=BIGINT},</if>
                <if test="originAmount != null">#{originAmount,jdbcType=BIGINT},</if>
                <if test="expectedInvoiceAmount != null">#{expectedInvoiceAmount,jdbcType=BIGINT},</if>
                <if test="productCode != null">#{productCode,jdbcType=VARCHAR},</if>
                <if test="productCount != null">#{productCount,jdbcType=INTEGER},</if>
                <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
                <if test="gmtCreate != null">#{gmtCreate,jdbcType=BIGINT},</if>
                <if test="gmtUpdate != null">#{gmtUpdate,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="so.dian.invoice.pojo.entity.InvoiceManageDetailDO">
        update invoice_manage_detail
        <set>
                <if test="manageId != null">
                    manage_id = #{manageId,jdbcType=BIGINT},
                </if>
                <if test="amount != null">
                    amount = #{amount,jdbcType=BIGINT},
                </if>
                <if test="originAmount != null">
                    origin_amount = #{originAmount,jdbcType=BIGINT},
                </if>
                <if test="expectedInvoiceAmount != null">
                    expected_invoice_amount = #{expectedInvoiceAmount,jdbcType=BIGINT},
                </if>
                <if test="productCode != null">
                    product_code = #{productCode,jdbcType=VARCHAR},
                </if>
                <if test="productCount != null">
                    product_count = #{productCount,jdbcType=INTEGER},
                </if>
                <if test="deleted != null">
                    deleted = #{deleted,jdbcType=TINYINT},
                </if>
                <if test="gmtCreate != null">
                    gmt_create = #{gmtCreate,jdbcType=BIGINT},
                </if>
                <if test="gmtUpdate != null">
                    gmt_update = #{gmtUpdate,jdbcType=BIGINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="so.dian.invoice.pojo.entity.InvoiceManageDetailDO">
        update invoice_manage_detail
        set 
            manage_id =  #{manageId,jdbcType=BIGINT},
            amount =  #{amount,jdbcType=BIGINT},
            origin_amount =  #{originAmount,jdbcType=BIGINT},
            expected_invoice_amount =  #{expectedInvoiceAmount,jdbcType=BIGINT},
            product_code =  #{productCode,jdbcType=VARCHAR},
            product_count =  #{productCount,jdbcType=INTEGER},
            gmt_update =  #{gmtUpdate,jdbcType=BIGINT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <select id="listByManageIds" resultType="so.dian.invoice.pojo.entity.InvoiceManageDetailDO">
        SELECT * FROM invoice_manage_detail
        WHERE deleted = 0 and manage_id IN
        <foreach item="id" index="index" collection="manageIds"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getByManageId" resultType="so.dian.invoice.pojo.entity.InvoiceManageDetailDO">
        SELECT * FROM invoice_manage_detail WHERE deleted = 0 and manage_id = #{manageId} order by id
    </select>

    <insert id="batchInsert" parameterType="list" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO invoice_manage_detail
        (manage_id, amount, origin_amount, expected_invoice_amount, product_code, product_count, deleted, gmt_create, gmt_update)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.manageId,jdbcType=BIGINT}, #{item.amount,jdbcType=BIGINT}, #{item.originAmount,jdbcType=BIGINT},
             #{item.expectedInvoiceAmount,jdbcType=BIGINT}, #{item.productCode,jdbcType=VARCHAR}, #{item.productCount,jdbcType=INTEGER},
             #{item.deleted,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=BIGINT}, #{item.gmtUpdate,jdbcType=BIGINT})
        </foreach>
    </insert>

</mapper>
