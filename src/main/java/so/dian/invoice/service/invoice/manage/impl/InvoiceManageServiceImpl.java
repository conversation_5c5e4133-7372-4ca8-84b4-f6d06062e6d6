package so.dian.invoice.service.invoice.manage.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.customer.dto.request.AuthenticationSubjectDTO;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.converter.InvoiceManageConverter;
import so.dian.invoice.dao.*;
import so.dian.invoice.dao.InvoiceChangeRecordDAO;
import so.dian.invoice.enums.AgentTypeEnum;
import so.dian.invoice.facade.PassportFacade;
import so.dian.invoice.facade.SunReaverFacade;
import so.dian.invoice.facade.TiantaiFacade;
import so.dian.invoice.handle.ExcelHandler;
import so.dian.invoice.manager.AgentManager;
import so.dian.invoice.manager.invoice.manage.InvoiceManageManager;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageBatchImportDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageBatchImportListener;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
import so.dian.invoice.pojo.entity.*;
import so.dian.invoice.pojo.entity.InvoiceChangeRecordDO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceManageBatchImportParam;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceManageQueryParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.AgentVO;
import so.dian.invoice.pojo.vo.AuthenticationSubjectVO;
import so.dian.invoice.pojo.vo.invoice.manage.ExportInvoiceManageDetailExcel;
import so.dian.invoice.pojo.vo.invoice.manage.ExportInvoiceManageExcel;
import so.dian.invoice.service.invoice.manage.InvoiceManageService;
import so.dian.invoice.util.OssUtil;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.URL;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 10:44
 */
@Slf4j
@Service
public class InvoiceManageServiceImpl implements InvoiceManageService {

    @Resource
    private InvoiceManageManager invoiceManageManager;
    @Resource
    private InvoiceChangeRecordDAO invoiceChangeRecordDAO;
    @Resource
    private ExcelHandler excelHandler;
    @Resource
    private AgentManager agentManager;
    @Resource
    private SunReaverFacade sunReaverManager;
    @Resource
    private TiantaiFacade tiantaiManager;
    @Resource
    private PassportFacade passportFacade;
    @Resource
    private InvoiceManageDAO invoiceManageDAO;
    @Resource
    private InvoiceManageDetailDAO invoiceManageDetailDAO;
    @Resource
    private InvoiceChangeRecordDetailDAO invoiceChangeRecordDetailDAO;
    @Resource
    private InvoiceRequestDAO invoiceRequestDAO;
    @Resource
    private InvoiceRequestRecordDAO invoiceRequestRecordDAO;
    @Resource
    private InvoiceRequestRecordDetailDAO invoiceRequestRecordDetailDAO;

    @Override
    public List<AuthenticationSubjectVO> getAuthenticationSubjectByAgentId(List<Long> manageIds, CurrentUserReq userReq){
        if (CollectionUtils.isEmpty(manageIds)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"未选择开票业务单");
        }
        // 判断用户角色是否是 BD主管、代理商、合资公司老板
//        if (!UserRoleEnum.AGENT_SELLER_MANAGER.getRoleName().equals(userReq.getCurrentRole())
//                && !UserRoleEnum.AGENCY_BOSS.getRoleName().equals(userReq.getCurrentRole())
//                && !UserRoleEnum.JOINTAFFAIRSMANAGER.getRoleName().equals(userReq.getCurrentRole())) {
//            log.info("只有 BD主管、代理商、合资公司老板才允许开票");
//            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"只有 BD主管、代理商、合资公司老板才允许开票");
////            return new ArrayList<>();
//        }

        //判断开票主体是否相同
        List<InvoiceManageDTO> list = invoiceManageManager.listManageById(manageIds);
        if (CollectionUtils.isEmpty(list)) {
            log.warn("根据业务单ID获取发票管理信息为空,manageIds:", JSON.toJSONString(manageIds));
            return new ArrayList<>();
        }
        Set<Long> set = list.stream().map(e -> e.getSubjectId()).collect(Collectors.toSet());
        if (set.size() > 1) {
            log.warn("申请开票勾选的业务单渠道商ID需要一致,manageIds:", JSON.toJSONString(manageIds));
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"申请开票勾选的业务单渠道商ID需要一致");
        }
        InvoiceManageDTO invoiceManage = list.get(0);
        Long subjectId = invoiceManage.getSubjectId();
        List<AuthenticationSubjectDTO>  authenticationSubjects = passportFacade.getAuthenticationSubjectByAgentId(subjectId);

        List<AuthenticationSubjectVO> voList = authenticationSubjects.stream().map(AuthenticationSubjectVO::fromDTO).collect(Collectors.toList());

        return voList;
    }
    @Override
    public PageInfo<InvoiceManageDTO> page(InvoiceManageQueryParam param, CurrentUserReq userReq){
        //场景            角色                              数据范围
        //小电财务	财务经理	                        可查看所有公司的应开数据，含一代、合资和二代
        //小电渠道经理	渠道经理、渠道总监	                仅可查看本人及下属员工负责的代理商的信息
        //小电运营	城市策划	                        仅可查看发布公司为本人及下属运营负责公司
        //渠道商老板	BD主管（且为老板身份）、代理商	仅可查看本人归属公司的应开数据
        //渠道商财务	合资公司财务经理
        // 查询发票管理
        // 分页
        // 校验参数中的主体id是否在相应的角色所负责的公司中
        if(!StringUtils.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), userReq.getCurrentRole())) {
            List<Long> list = invoiceManageManager.getAgentIds(userReq);
            if (Objects.nonNull(param) && CollectionUtils.isNotEmpty(param.getSubjectIds())) {
                boolean b = param.getSubjectIds().stream().anyMatch(e -> !list.contains(e));
                if (b) {
                    throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"参数渠道商信息中存在未授权的渠道商ID");
                }
            }else {
                // 根据角色的不同只能查看指定范围的公司
                param.setSubjectIds(list);
            }
        }
        //空字符串处理
        if (StringUtils.isBlank(param.getBizNo())) {
            param.setBizNo(null);
        }
        Page<Object> page = PageMethod.startPage(param.getPageNo(), param.getPageSize());
        //查询发票管理
        List<InvoiceManageDTO> manages = invoiceManageManager.list(param);
        // 构建返回值
        PageInfo<InvoiceManageDTO> pageInfo = new PageInfo<>(manages);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    /**
     * 根据用户ID
     * 场景            角色                              数据范围
     * 小电财务	财务经理	                        可查看所有公司的应开数据，含一代、合资和二代
     * 小电渠道经理	渠道经理、渠道总监	        仅可查看本人及下属员工负责的代理商的信息
     * 小电运营	城市策划	                        仅可查看发布公司为本人及下属运营负责公司
     * 渠道商老板	BD主管（且为老板身份）、代理商	仅可查看本人归属公司的应开数据
     * 渠道商财务	合资公司财务经理                   仅可查看本人归属公司的应开数据
     *
     * @param userReq
     * @return
     */
    @Override
    public List<AgentVO> getAgentInfo(CurrentUserReq userReq){
        //财务经理 可查看所有公司的应开数据，含一代、合资和二代
        if(StringUtils.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), userReq.getCurrentRole())){
	    List<AgentDTO> agentDTOS = agentManager.findAgentList();
            List<AgentVO> agentVOS = agentDTOS.stream().map(AgentVO::fromDTO).collect(Collectors.toList());
	    return agentVOS;
	}
        // 非财务经理角色查看到的公司范围
        List<Long> agentIds = invoiceManageManager.getAgentIds(userReq);
        if (CollectionUtils.isEmpty(agentIds)) {
            log.warn("远程获取代理商信息 | 公司不存在 | userId:{},role:{}", userReq.getUserId(), userReq.getCurrentRole());
            return new ArrayList<>();
        }
        List<AgentDTO> agentDTOS = agentManager.listByIds(agentIds);
        if (CollectionUtils.isEmpty(agentDTOS)) {
            log.warn("远程获取代理商信息 | 用户不存在 | agentIds:{}", agentIds);
            return new ArrayList<>();
        }
        List<AgentVO> agentVOS = agentDTOS.stream()
                .filter(e -> AgentTypeEnum.AGENT_TYPE.getId().equals(e.getType()) || AgentTypeEnum.JV_COMPANY_TYPE.getId().equals(e.getType()))
                .map(AgentVO::fromDTO).collect(Collectors.toList());
        return agentVOS;
    }

    @Override
    public List<InvoiceChangeRecordDTO> listExpectedInvoiceAmountChangeRecord(Long manageId){
        List<InvoiceChangeRecordDO> changeRecordDOS = invoiceChangeRecordDAO.listByManageId(manageId);
        List<InvoiceChangeRecordDTO> changeRecordDTOS = changeRecordDOS.stream().map(InvoiceChangeRecordDTO::fromDO).collect(Collectors.toList());
        // 查询业务单号
        List<Long> manageIds = changeRecordDTOS.stream().map(e -> e.getManageId()).collect(Collectors.toList());
        List<InvoiceManageDTO> manageDTOS = invoiceManageManager.listManageById(manageIds);
        Map<Long, InvoiceManageDTO> map = manageDTOS.stream().collect(Collectors.toMap(InvoiceManageDTO::getId, Function.identity(), (v1, v2) -> v1));
        for (InvoiceChangeRecordDTO changeRecordDTO : changeRecordDTOS) {
            InvoiceManageDTO manageDTO = map.get(changeRecordDTO.getManageId());
            if(Objects.nonNull(manageDTO)){
                changeRecordDTO.setBizNo(manageDTO.getBizNo());
                changeRecordDTO.setBizType(manageDTO.getBizType());
            }
        }
        return changeRecordDTOS;
    }

    @Override
    public List<InvoiceManageDetailDTO> getInvoiceManageDetailById(Long manageId){
        List<InvoiceManageDetailDTO> manageDetails = invoiceManageManager.getInvoiceManageDetailById(manageId);
        return manageDetails;
    }

    /**
     * 导出
     */
    @Override
    public void export(InvoiceManageQueryParam param, CurrentUserReq userReq) {
        if (StringUtils.isBlank(userReq.getEmail())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"操作人邮箱不存在，请联系运营或HR更新邮箱后重试");
        }
        // 权限校验
        if(!StringUtils.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), userReq.getCurrentRole())) {
            List<Long> agentIds = invoiceManageManager.getAgentIds(userReq);
            if (Objects.nonNull(param) && CollectionUtils.isNotEmpty(param.getSubjectIds())) {
                boolean b = param.getSubjectIds().stream().anyMatch(e -> !agentIds.contains(e));
                if (b) {
                    throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"参数渠道商信息中存在未授权的渠道商ID");
                }
            }else {
                // 根据角色的不同只能查看指定范围的公司
                param.setSubjectIds(agentIds);
            }
        }

        List<InvoiceManageDTO> list = invoiceManageManager.list(param);
        if (CollectionUtils.isEmpty(list)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"没有要导出的数据");
        }
        Integer belongSubjectId = userReq.getBelongSubjectId();
        //填充产品名称
        List<String> spuCodes = list.stream().filter(e -> CollectionUtils.isNotEmpty(e.getSpuCodes())).map(e -> e.getSpuCodes()).flatMap(List::stream).collect(Collectors.toList());
        Map<String, String> map = tiantaiManager.getSpuName(spuCodes);
        list.forEach(e -> {
            e.fillProductName(map);
        });
        try {
            // 小电员工
            if (InvoiceConstants.DIAN_INVOICE_BELONG_ID.contains(belongSubjectId)) {
                //构建开票申请记录
                List<ExportInvoiceManageDetailExcel> excelVOList = InvoiceManageConverter.toDetailExcelBean(list);
                excelHandler.exportToEmail(excelVOList, ExportInvoiceManageDetailExcel.class, userReq);
            }else {
                //构建开票申请记录
                List<ExportInvoiceManageExcel> excelVOList = InvoiceManageConverter.toExcelBean(list);
                excelHandler.exportToEmail(excelVOList, ExportInvoiceManageExcel.class, userReq);
            }
        } catch (Exception e) {
            log.info("导出开票申请记录出错", e);
            throw BizException.create(BaseErrorCodeEnum.FALLBACK,"发送邮件失败");
        }
    }

    @Override
    public boolean batchImport(InvoiceManageBatchImportParam param, CurrentUserReq userReq) {
        log.info("开始批量导入应开明细，fileUrl={}, operator={}", param.getFileUrl(), userReq.getUserName());

        try {
            // 1. 从文件URL下载Excel文件
            InputStream inputStream = downloadFileFromUrl(param.getFileUrl());

            // 2. 使用EasyExcel解析数据并处理
            InvoiceManageBatchImportListener listener = new InvoiceManageBatchImportListener(this);
            EasyExcel.read(inputStream, InvoiceManageBatchImportDTO.class, listener).sheet().doRead();

            // 3. 检查是否有失败的数据
            if (!listener.getFailedData().isEmpty()) {
                log.error("批量导入存在失败数据，失败数量：{}", listener.getFailedData().size());
                // 这里可以考虑将失败数据写入文件或返回给前端
                return false;
            }

            log.info("批量导入应开明细成功，总数据量：{}", listener.getAllDataSize());
            return true;

        } catch (Exception e) {
            log.error("批量导入应开明细失败", e);
            throw BizException.create(BaseErrorCodeEnum.UNKNOWN_ERROR, "批量导入失败：" + e.getMessage());
        }
    }

    /**
     * 从URL下载文件
     */
    private InputStream downloadFileFromUrl(String fileUrl) throws Exception {
        if (StringUtils.isBlank(fileUrl)) {
            throw new IllegalArgumentException("文件URL不能为空");
        }

        // 如果是OSS URL，获取带权限的URL
        String downloadUrl = fileUrl;
        if (fileUrl.contains("oss-cn-")) {
            downloadUrl = OssUtil.getOneHourAclUrl(fileUrl);
        }

        URL url = new URL(downloadUrl);
        return url.openStream();
    }

    /**
     * 处理批量导入数据（供监听器调用）
     */
    public void processBatchImportData(List<InvoiceManageBatchImportDTO> dataList) throws Exception {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        log.info("开始处理批量导入数据，数据量：{}", dataList.size());

        // 执行校验
        validateBatchImportData(dataList);

        // 按业务单号分组，合并相同业务单号的产品信息
        Map<String, List<InvoiceManageBatchImportDTO>> bizNoGroupMap = dataList.stream()
            .collect(Collectors.groupingBy(InvoiceManageBatchImportDTO::getBizNo));

        // 处理每个业务单号的数据
        for (Map.Entry<String, List<InvoiceManageBatchImportDTO>> entry : bizNoGroupMap.entrySet()) {
            String bizNo = entry.getKey();
            List<InvoiceManageBatchImportDTO> bizDataList = entry.getValue();

            // 处理单个业务单号的数据
            processSingleBizNoData(bizNo, bizDataList);
        }

        log.info("批量导入数据处理完成");
    }

    /**
     * 执行校验
     */
    private void validateBatchImportData(List<InvoiceManageBatchImportDTO> dataList) throws Exception {
        // 2.1 业务单号要跟系统中已有的应开明细的业务单号不重复
        Set<String> bizNoSet = dataList.stream().map(InvoiceManageBatchImportDTO::getBizNo).collect(Collectors.toSet());
        for (String bizNo : bizNoSet) {
            if (invoiceManageManager.getByBizNoAndBizType(bizNo, 1) != null) {
                throw new Exception("业务单号" + bizNo + "已存在，不能重复导入");
            }
        }

        // 2.2 渠道商ID需校验系统中是否存在
        Set<Long> subjectIdSet = dataList.stream().map(InvoiceManageBatchImportDTO::getSubjectId).collect(Collectors.toSet());
        for (Long subjectId : subjectIdSet) {
            AgentDTO agentDTO = agentManager.findById(subjectId.intValue());
            if (agentDTO == null) {
                throw new Exception("渠道商ID" + subjectId + "不存在");
            }
        }

        // 2.3 待开票总金额需=应开票总金额-开票中总金额-已开票总金额
        // 2.4 待开票金额需=应开票金额-开票中金额-已开票金额
        for (InvoiceManageBatchImportDTO data : dataList) {
            // 校验总金额
            long expectedPendingTotal = data.getExpectedInvoiceAmountCent() - data.getOngoingInvoiceAmountCent() - data.getCompletedInvoiceAmountCent();
            if (!data.getPendingInvoiceAmountCent().equals(expectedPendingTotal)) {
                throw new Exception("业务单号" + data.getBizNo() + "的待开票总金额计算错误");
            }

            // 校验明细金额
            long expectedPendingDetail = data.getExpectedAmountCent() - data.getOngoingAmountCent() - data.getCompletedAmountCent();
            if (!data.getPendingAmountCent().equals(expectedPendingDetail)) {
                throw new Exception("业务单号" + data.getBizNo() + "的待开票金额计算错误");
            }
        }
    }

    /**
     * 处理单个业务单号的数据
     */
    private void processSingleBizNoData(String bizNo, List<InvoiceManageBatchImportDTO> bizDataList) throws Exception {
        if (CollectionUtils.isEmpty(bizDataList)) {
            return;
        }

        // 取第一条记录的基础信息（其他字段取第一条记录的字段信息）
        InvoiceManageBatchImportDTO firstRecord = bizDataList.get(0);

        // 3. 新增应开明细
        Long manageId = createInvoiceManage(firstRecord);

        // 新增应开产品明细（合并产品信息到同一个应开明细下）
        createInvoiceManageDetails(manageId, bizDataList);

        // 4. 若应开金额>0，则新增应开变更记录和应开变更记录详情
        if (firstRecord.getExpectedInvoiceAmountCent() > 0) {
            createInvoiceChangeRecord(manageId, firstRecord);
        }

        // 5. 若已开金额>0，则新增开票申请、开票申请记录和开票申请记录明细
        if (firstRecord.getCompletedInvoiceAmountCent() > 0) {
            createInvoiceRequest(manageId, firstRecord, bizDataList);
        }

        log.info("处理业务单号{}的数据完成，产品数量：{}", bizNo, bizDataList.size());
    }

    /**
     * 创建应开明细
     */
    private Long createInvoiceManage(InvoiceManageBatchImportDTO data) {
        InvoiceManageDO manageDO = new InvoiceManageDO();
        manageDO.setBizNo(data.getBizNo());
        manageDO.setBizType(data.getBizType());
        manageDO.setSubjectId(data.getSubjectId());
        manageDO.setSubjectType(data.getSubjectType());
        manageDO.setTotalAmount(data.getTotalAmountCent());
        manageDO.setPaymentTime(data.getPaymentTimestamp());
        manageDO.setBizCreateTime(data.getBizCreateTimestamp());
        manageDO.setStatus(0); // 初始状态
        manageDO.init();

        invoiceManageDAO.insertSelective(manageDO);
        return manageDO.getId();
    }

    /**
     * 创建应开产品明细
     */
    private void createInvoiceManageDetails(Long manageId, List<InvoiceManageBatchImportDTO> dataList) {
        for (InvoiceManageBatchImportDTO data : dataList) {
            InvoiceManageDetailDO detailDO = new InvoiceManageDetailDO();
            detailDO.setManageId(manageId);
            detailDO.setAmount(data.getAmountCent());
            detailDO.setOriginAmount(data.getAmountCent()); // 原始金额等于业务单金额
            detailDO.setExpectedInvoiceAmount(data.getExpectedAmountCent());
            detailDO.setProductCode(data.getProductCode());
            detailDO.setProductCount(data.getProductCount());
            detailDO.init();

            invoiceManageDetailDAO.insertSelective(detailDO);
        }
    }

    /**
     * 创建应开变更记录
     */
    private void createInvoiceChangeRecord(Long manageId, InvoiceManageBatchImportDTO data) {
        // 创建应开变更记录
        InvoiceChangeRecordDO recordDO = new InvoiceChangeRecordDO();
        recordDO.setManageId(manageId);
        recordDO.setOutBizId(data.getBizNo());
        recordDO.setOutBizType(data.getBizType());
//        recordDO.setChangeType(1); // 金额变更
//        recordDO.setChangeReason("批量导入初始化");
//        recordDO.setOperator("system");
//        recordDO.setOperateTime(System.currentTimeMillis());
        recordDO.init();

        invoiceChangeRecordDAO.insertSelective(recordDO);

        // 创建应开变更记录详情
        InvoiceChangeRecordDetailDO detailDO = new InvoiceChangeRecordDetailDO();
//        detailDO.setRecordId(recordDO.getId());
        detailDO.setManageId(manageId);
//        detailDO.setBeforeAmount(0L); // 变更前金额为0
//        detailDO.setAfterAmount(data.getExpectedInvoiceAmountCent()); // 变更后金额
//        detailDO.setChangeAmount(data.getExpectedInvoiceAmountCent()); // 变更金额
        detailDO.init();

        invoiceChangeRecordDetailDAO.insertSelective(detailDO);
    }

    /**
     * 创建开票申请
     */
    private void createInvoiceRequest(Long manageId, InvoiceManageBatchImportDTO firstRecord,
                                    List<InvoiceManageBatchImportDTO> dataList) {
        // 创建开票申请
        InvoiceRequestDO requestDO = new InvoiceRequestDO();
        requestDO.setSubjectId(firstRecord.getSubjectId());
        requestDO.setSubjectType(firstRecord.getSubjectType());
        requestDO.setSubjectName(firstRecord.getSubjectName());
//        requestDO.setTotalAmount(firstRecord.getCompletedInvoiceAmountCent());
        requestDO.setStatus(2); // 已开票状态
//        requestDO.setApplicant("system");
//        requestDO.setApplyTime(System.currentTimeMillis());
        requestDO.setInvoiceCompletedTime(System.currentTimeMillis());
        requestDO.init();

        invoiceRequestDAO.insertSelective(requestDO);

        // 创建开票申请记录
        InvoiceRequestRecordDO recordDO = new InvoiceRequestRecordDO();
        recordDO.setRequestId(requestDO.getId());
        recordDO.setManageId(manageId);
        recordDO.setAmount(firstRecord.getCompletedInvoiceAmountCent());
        recordDO.setStatus(2); // 已开票状态
        recordDO.init();

        invoiceRequestRecordDAO.insertSelective(recordDO);

        // 创建开票申请记录明细
        for (InvoiceManageBatchImportDTO data : dataList) {
            if (data.getCompletedAmountCent() > 0) {
                InvoiceRequestRecordDetailDO detailDO = new InvoiceRequestRecordDetailDO();
                detailDO.setRequestRecordId(recordDO.getId());
                detailDO.setManageId(manageId);
                detailDO.setAmount(data.getCompletedAmountCent());
                detailDO.setProductCode(data.getProductCode());
                detailDO.setProductCount(data.getProductCount());
                detailDO.setStatus(2); // 已开票状态
                detailDO.init();

                invoiceRequestRecordDetailDAO.insertSelective(detailDO);
            }
        }
    }
}