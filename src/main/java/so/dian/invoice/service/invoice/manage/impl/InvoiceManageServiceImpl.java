package so.dian.invoice.service.invoice.manage.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.common.util.angel.excel.AngelBatchRowHandler;
import so.dian.common.util.angel.excel.AngelExcel;
import so.dian.customer.dto.request.AuthenticationSubjectDTO;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;
import so.dian.himalaya.common.exception.BizException;
import so.dian.hr.api.entity.common.UserRoleEnum;
import so.dian.invoice.constant.InvoiceConstants;
import so.dian.invoice.converter.InvoiceManageConverter;
import so.dian.invoice.dao.*;
import so.dian.invoice.dao.InvoiceChangeRecordDAO;
import so.dian.invoice.enums.*;
import so.dian.invoice.facade.PassportFacade;
import so.dian.invoice.facade.SunReaverFacade;
import so.dian.invoice.facade.TiantaiFacade;
import so.dian.invoice.handle.ExcelHandler;
import so.dian.invoice.manager.AgentManager;
import so.dian.invoice.manager.invoice.manage.InvoiceManageManager;
import so.dian.invoice.pojo.dto.angel.InvoiceManageBatchImportADTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceChangeRecordDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageBatchImportDTO;
import so.dian.invoice.pojo.dto.invoice.manage.InvoiceManageDetailDTO;
import so.dian.invoice.pojo.entity.*;
import so.dian.invoice.pojo.entity.InvoiceChangeRecordDO;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceManageBatchImportParam;
import so.dian.invoice.pojo.param.Invoice.manage.InvoiceManageQueryParam;
import so.dian.invoice.pojo.request.CurrentUserReq;
import so.dian.invoice.pojo.vo.AgentVO;
import so.dian.invoice.pojo.vo.AuthenticationSubjectVO;
import so.dian.invoice.pojo.vo.invoice.manage.ExportInvoiceManageDetailExcel;
import so.dian.invoice.pojo.vo.invoice.manage.ExportInvoiceManageExcel;
import so.dian.invoice.service.invoice.manage.InvoiceManageService;
import so.dian.invoice.util.FractionUtils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @program: invoice
 * @description:
 * @author: yuechuan
 * @create: 2025-03-12 10:44
 */
@Slf4j
@Service
public class InvoiceManageServiceImpl implements InvoiceManageService {

    @Resource
    private InvoiceManageManager invoiceManageManager;
    @Resource
    private InvoiceChangeRecordDAO invoiceChangeRecordDAO;
    @Resource
    private ExcelHandler excelHandler;
    @Resource
    private AgentManager agentManager;
    @Resource
    private SunReaverFacade sunReaverManager;
    @Resource
    private TiantaiFacade tiantaiManager;
    @Resource
    private PassportFacade passportFacade;
    @Resource
    private InvoiceManageDAO invoiceManageDAO;
    @Resource
    private InvoiceManageDetailDAO invoiceManageDetailDAO;
    @Resource
    private InvoiceChangeRecordDetailDAO invoiceChangeRecordDetailDAO;
    @Resource
    private InvoiceRequestDAO invoiceRequestDAO;
    @Resource
    private InvoiceRequestRecordDAO invoiceRequestRecordDAO;
    @Resource
    private InvoiceRequestRecordDetailDAO invoiceRequestRecordDetailDAO;

    /**
     * 时间格式
     */
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 金额格式正则
     */
    private static final Pattern AMOUNT_PATTERN = Pattern.compile("^\\d+(\\.\\d{1,2})?$");

    /**
     * 批量处理的数量
     */
    private static final int BATCH_NUM = 1000;

    @Override
    public List<AuthenticationSubjectVO> getAuthenticationSubjectByAgentId(List<Long> manageIds, CurrentUserReq userReq){
        if (CollectionUtils.isEmpty(manageIds)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"未选择开票业务单");
        }
        // 判断用户角色是否是 BD主管、代理商、合资公司老板
//        if (!UserRoleEnum.AGENT_SELLER_MANAGER.getRoleName().equals(userReq.getCurrentRole())
//                && !UserRoleEnum.AGENCY_BOSS.getRoleName().equals(userReq.getCurrentRole())
//                && !UserRoleEnum.JOINTAFFAIRSMANAGER.getRoleName().equals(userReq.getCurrentRole())) {
//            log.info("只有 BD主管、代理商、合资公司老板才允许开票");
//            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"只有 BD主管、代理商、合资公司老板才允许开票");
////            return new ArrayList<>();
//        }

        //判断开票主体是否相同
        List<InvoiceManageDTO> list = invoiceManageManager.listManageById(manageIds);
        if (CollectionUtils.isEmpty(list)) {
            log.warn("根据业务单ID获取发票管理信息为空,manageIds:", JSON.toJSONString(manageIds));
            return new ArrayList<>();
        }
        Set<Long> set = list.stream().map(e -> e.getSubjectId()).collect(Collectors.toSet());
        if (set.size() > 1) {
            log.warn("申请开票勾选的业务单渠道商ID需要一致,manageIds:", JSON.toJSONString(manageIds));
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"申请开票勾选的业务单渠道商ID需要一致");
        }
        InvoiceManageDTO invoiceManage = list.get(0);
        Long subjectId = invoiceManage.getSubjectId();
        List<AuthenticationSubjectDTO>  authenticationSubjects = passportFacade.getAuthenticationSubjectByAgentId(subjectId);

        List<AuthenticationSubjectVO> voList = authenticationSubjects.stream().map(AuthenticationSubjectVO::fromDTO).collect(Collectors.toList());

        return voList;
    }
    @Override
    public PageInfo<InvoiceManageDTO> page(InvoiceManageQueryParam param, CurrentUserReq userReq){
        //场景            角色                              数据范围
        //小电财务	财务经理	                        可查看所有公司的应开数据，含一代、合资和二代
        //小电渠道经理	渠道经理、渠道总监	                仅可查看本人及下属员工负责的代理商的信息
        //小电运营	城市策划	                        仅可查看发布公司为本人及下属运营负责公司
        //渠道商老板	BD主管（且为老板身份）、代理商	仅可查看本人归属公司的应开数据
        //渠道商财务	合资公司财务经理
        // 查询发票管理
        // 分页
        // 校验参数中的主体id是否在相应的角色所负责的公司中
        if(!StringUtils.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), userReq.getCurrentRole())) {
            List<Long> list = invoiceManageManager.getAgentIds(userReq);
            if (Objects.nonNull(param) && CollectionUtils.isNotEmpty(param.getSubjectIds())) {
                boolean b = param.getSubjectIds().stream().anyMatch(e -> !list.contains(e));
                if (b) {
                    throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"参数渠道商信息中存在未授权的渠道商ID");
                }
            }else {
                // 根据角色的不同只能查看指定范围的公司
                param.setSubjectIds(list);
            }
        }
        //空字符串处理
        if (StringUtils.isBlank(param.getBizNo())) {
            param.setBizNo(null);
        }
        Page<Object> page = PageMethod.startPage(param.getPageNo(), param.getPageSize());
        //查询发票管理
        List<InvoiceManageDTO> manages = invoiceManageManager.list(param);
        // 构建返回值
        PageInfo<InvoiceManageDTO> pageInfo = new PageInfo<>(manages);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    /**
     * 根据用户ID
     * 场景            角色                              数据范围
     * 小电财务	财务经理	                        可查看所有公司的应开数据，含一代、合资和二代
     * 小电渠道经理	渠道经理、渠道总监	        仅可查看本人及下属员工负责的代理商的信息
     * 小电运营	城市策划	                        仅可查看发布公司为本人及下属运营负责公司
     * 渠道商老板	BD主管（且为老板身份）、代理商	仅可查看本人归属公司的应开数据
     * 渠道商财务	合资公司财务经理                   仅可查看本人归属公司的应开数据
     *
     * @param userReq
     * @return
     */
    @Override
    public List<AgentVO> getAgentInfo(CurrentUserReq userReq){
        //财务经理 可查看所有公司的应开数据，含一代、合资和二代
        if(StringUtils.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), userReq.getCurrentRole())){
	    List<AgentDTO> agentDTOS = agentManager.findAgentList();
            List<AgentVO> agentVOS = agentDTOS.stream().map(AgentVO::fromDTO).collect(Collectors.toList());
	    return agentVOS;
	}
        // 非财务经理角色查看到的公司范围
        List<Long> agentIds = invoiceManageManager.getAgentIds(userReq);
        if (CollectionUtils.isEmpty(agentIds)) {
            log.warn("远程获取代理商信息 | 公司不存在 | userId:{},role:{}", userReq.getUserId(), userReq.getCurrentRole());
            return new ArrayList<>();
        }
        List<AgentDTO> agentDTOS = agentManager.listByIds(agentIds);
        if (CollectionUtils.isEmpty(agentDTOS)) {
            log.warn("远程获取代理商信息 | 用户不存在 | agentIds:{}", agentIds);
            return new ArrayList<>();
        }
        List<AgentVO> agentVOS = agentDTOS.stream()
                .filter(e -> AgentTypeEnum.AGENT_TYPE.getId().equals(e.getType()) || AgentTypeEnum.JV_COMPANY_TYPE.getId().equals(e.getType()))
                .map(AgentVO::fromDTO).collect(Collectors.toList());
        return agentVOS;
    }

    @Override
    public List<InvoiceChangeRecordDTO> listExpectedInvoiceAmountChangeRecord(Long manageId){
        List<InvoiceChangeRecordDO> changeRecordDOS = invoiceChangeRecordDAO.listByManageId(manageId);
        List<InvoiceChangeRecordDTO> changeRecordDTOS = changeRecordDOS.stream().map(InvoiceChangeRecordDTO::fromDO).collect(Collectors.toList());
        // 查询业务单号
        List<Long> manageIds = changeRecordDTOS.stream().map(e -> e.getManageId()).collect(Collectors.toList());
        List<InvoiceManageDTO> manageDTOS = invoiceManageManager.listManageById(manageIds);
        Map<Long, InvoiceManageDTO> map = manageDTOS.stream().collect(Collectors.toMap(InvoiceManageDTO::getId, Function.identity(), (v1, v2) -> v1));
        for (InvoiceChangeRecordDTO changeRecordDTO : changeRecordDTOS) {
            InvoiceManageDTO manageDTO = map.get(changeRecordDTO.getManageId());
            if(Objects.nonNull(manageDTO)){
                changeRecordDTO.setBizNo(manageDTO.getBizNo());
                changeRecordDTO.setBizType(manageDTO.getBizType());
            }
        }
        return changeRecordDTOS;
    }

    @Override
    public List<InvoiceManageDetailDTO> getInvoiceManageDetailById(Long manageId){
        List<InvoiceManageDetailDTO> manageDetails = invoiceManageManager.getInvoiceManageDetailById(manageId);
        return manageDetails;
    }

    /**
     * 导出
     */
    @Override
    public void export(InvoiceManageQueryParam param, CurrentUserReq userReq) {
        if (StringUtils.isBlank(userReq.getEmail())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"操作人邮箱不存在，请联系运营或HR更新邮箱后重试");
        }
        // 权限校验
        if(!StringUtils.equals(UserRoleEnum.AFFAIRS_MANAGER.getRoleName(), userReq.getCurrentRole())) {
            List<Long> agentIds = invoiceManageManager.getAgentIds(userReq);
            if (Objects.nonNull(param) && CollectionUtils.isNotEmpty(param.getSubjectIds())) {
                boolean b = param.getSubjectIds().stream().anyMatch(e -> !agentIds.contains(e));
                if (b) {
                    throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"参数渠道商信息中存在未授权的渠道商ID");
                }
            }else {
                // 根据角色的不同只能查看指定范围的公司
                param.setSubjectIds(agentIds);
            }
        }

        List<InvoiceManageDTO> list = invoiceManageManager.list(param);
        if (CollectionUtils.isEmpty(list)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR,"没有要导出的数据");
        }
        Integer belongSubjectId = userReq.getBelongSubjectId();
        //填充产品名称
        List<String> spuCodes = list.stream().filter(e -> CollectionUtils.isNotEmpty(e.getSpuCodes())).map(e -> e.getSpuCodes()).flatMap(List::stream).collect(Collectors.toList());
        Map<String, String> map = tiantaiManager.getSpuName(spuCodes);
        list.forEach(e -> {
            e.fillProductName(map);
        });
        try {
            // 小电员工
            if (InvoiceConstants.DIAN_INVOICE_BELONG_ID.contains(belongSubjectId)) {
                //构建开票申请记录
                List<ExportInvoiceManageDetailExcel> excelVOList = InvoiceManageConverter.toDetailExcelBean(list);
                excelHandler.exportToEmail(excelVOList, ExportInvoiceManageDetailExcel.class, userReq);
            }else {
                //构建开票申请记录
                List<ExportInvoiceManageExcel> excelVOList = InvoiceManageConverter.toExcelBean(list);
                excelHandler.exportToEmail(excelVOList, ExportInvoiceManageExcel.class, userReq);
            }
        } catch (Exception e) {
            log.info("导出开票申请记录出错", e);
            throw BizException.create(BaseErrorCodeEnum.FALLBACK,"发送邮件失败");
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public boolean batchImport(InvoiceManageBatchImportParam param, CurrentUserReq userReq) {
        log.info("开始批量导入应开明细，fileUrl={}, operator={}", param.getFileUrl(), userReq.getUserName());

        try {
            // 预处理文件和校验
            preValidFile(param.getFileUrl());
            // 处理导入文件
            handleImportFile(param.getFileUrl());
        } catch (Throwable t) {
            log.error("应开明细批量导入发生异常，文件URL={}，异常原因={}", param.getFileUrl(), t.getMessage());
            throw t;
        }

        return true;
    }

    private Map<String, String> getBatchImportHeaderAliasMap() {
        Map<String, String> headerAliasMap = new HashMap<>();
        headerAliasMap.put("业务单号", "bizNo");
        headerAliasMap.put("业务类型（1 设备采购）", "bizType");
        headerAliasMap.put("渠道商类型（0代理商 5合资）", "subjectType");
        headerAliasMap.put("渠道商ID", "subjectId");
        headerAliasMap.put("渠道商名称", "subjectName");
        headerAliasMap.put("业务单总金额（元）（保留两位小数）", "totalAmount");
        headerAliasMap.put("应开票总金额（元）（保留两位小数）", "expectedInvoiceAmount");
        headerAliasMap.put("开票中总金额（元）（保留两位小数）", "ongoingInvoiceAmount");
        headerAliasMap.put("已开票总金额（元）（保留两位小数）", "completedInvoiceAmount");
        headerAliasMap.put("待开票总金额（元）（保留两位小数）", "pendingInvoiceAmount");
        headerAliasMap.put("业务单创建时间（2025-08-08 12:46:17）", "bizCreateTime");
        headerAliasMap.put("业务单付款完成时间（2025-08-08 12:46:17）", "paymentTime");
        headerAliasMap.put("产品名称（SPUCODE，如TC000013）", "productCode");
        headerAliasMap.put("产品数量（非负整数）", "productCount");
        headerAliasMap.put("业务单金额（元）（保留两位小数）", "amount");
        headerAliasMap.put("应开票金额（元）（保留两位小数）", "expectedAmount");
        headerAliasMap.put("开票中金额（元）（保留两位小数）", "ongoingAmount");
        headerAliasMap.put("已开票金额（元）（保留两位小数）", "completedAmount");
        headerAliasMap.put("待开票金额（元）（保留两位小数）", "pendingAmount");
        return headerAliasMap;
    }

    private InvoiceManageDO buildInvoiceManageDO(InvoiceManageBatchImportADTO data) {
        InvoiceManageDO manageDO = new InvoiceManageDO();
        manageDO.setBizNo(data.getBizNo());
        manageDO.setBizType(data.getBizType());
        manageDO.setSubjectId(data.getSubjectId());
        manageDO.setSubjectType(data.getSubjectType());
        // 元转分
        manageDO.setTotalAmount(FractionUtils.conventAmountToFen(data.getTotalAmount()));
        // 时间格式转换
        try {
            manageDO.setPaymentTime(DATE_FORMAT.parse(data.getPaymentTime()).getTime());
        } catch (ParseException e) {
            log.error("解析付款时间失败: {}", data.getPaymentTime(), e);
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "付款时间格式错误");
        }
        // 时间格式转换
        try {
            manageDO.setBizCreateTime(DATE_FORMAT.parse(data.getBizCreateTime()).getTime());
        } catch (ParseException e) {
            log.error("解析业务单创建时间失败: {}", data.getBizCreateTime(), e);
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "业务单创建时间格式错误");
        }

        if (data.getTotalAmount().compareTo(data.getCompletedInvoiceAmount()) == 0) {
            // 业务单总金额=已开票总金额，状态=开票完成
            manageDO.setStatus(InvoiceManageStatusEnum.COMPLETED_INVOICE.getCode());
        } else {
            // 其余情况，状态=开票中
            manageDO.setStatus(InvoiceManageStatusEnum.PENDING_INVOICE.getCode());
        }

        manageDO.init();
        return manageDO;
    }

    private List<InvoiceManageDetailDO> buildInvoiceManageDetailDO(Long manageId, List<InvoiceManageBatchImportADTO> dataList) {
        return dataList.stream().map(item -> {
            InvoiceManageDetailDO detailDO = new InvoiceManageDetailDO();
            detailDO.setManageId(manageId);
            // 元转分
            detailDO.setAmount(FractionUtils.conventAmountToFen(item.getAmount()));
            // 元转分
            detailDO.setOriginAmount(FractionUtils.conventAmountToFen(item.getAmount()));
            // 元转分
            detailDO.setExpectedInvoiceAmount(FractionUtils.conventAmountToFen(item.getExpectedAmount()));
            detailDO.setProductCode(item.getProductCode());
            detailDO.setProductCount(item.getProductCount());
            detailDO.init();
            return detailDO;
        }).collect(Collectors.toList());
    }

    private InvoiceChangeRecordDO buildInvoiceChangeRecordDO(Long manageId, InvoiceManageBatchImportADTO data) {
        InvoiceChangeRecordDO recordDO = new InvoiceChangeRecordDO();
        recordDO.setOutBizId("");
        recordDO.setOutBizType(OutBizTypeEnum.INIT.getCode());
        recordDO.setManageId(manageId);
        recordDO.setType(ChangeTypeEnum.INCREASE.getCode());
        // 元转分
        recordDO.setAmount(FractionUtils.conventAmountToFen(data.getExpectedInvoiceAmount()));
        recordDO.init();
        return recordDO;
    }

    private List<InvoiceChangeRecordDetailDO> buildInvoiceChangeRecordDetailDO(Long manageId, Long recordId, List<InvoiceManageDetailDO> manageDetailDOList) {
        return manageDetailDOList.stream().map(item -> {
            InvoiceChangeRecordDetailDO detailDO = new InvoiceChangeRecordDetailDO();
            detailDO.setChangeRecordId(recordId);
            detailDO.setManageId(manageId);
            detailDO.setManageDetailId(item.getId());
            detailDO.setProductCode(item.getProductCode());
            detailDO.setProductCount(item.getProductCount());
            detailDO.setType(ChangeTypeEnum.INCREASE.getCode());
            detailDO.setAmount(item.getExpectedInvoiceAmount());
            detailDO.init();
            return detailDO;
        }).collect(Collectors.toList());
    }

    private InvoiceRequestDO buildInvoiceRequestDO(InvoiceManageBatchImportADTO importADTO) {
        InvoiceRequestDO requestDO = new InvoiceRequestDO();
        requestDO.setSubjectId(importADTO.getSubjectId());
        requestDO.setSubjectType(importADTO.getSubjectType());
        requestDO.setSubjectName(importADTO.getSubjectName());
//        requestDO.setTitleType(); // TitleTypeEnum // 查agent属于个人还是企业
        // 元转分
        requestDO.setAmount(FractionUtils.conventAmountToFen(importADTO.getCompletedInvoiceAmount()));
        requestDO.setApplicantId(0L);
        requestDO.setStatus(InvoiceRequestStatusEnum.COMPLETED_INVOICE.getCode());
        requestDO.setInvoiceCompletedTime(System.currentTimeMillis());
        requestDO.init();
        return requestDO;
    }

    private InvoiceRequestRecordDO buildInvoiceRequestRecordDO(InvoiceManageDO manageDO, InvoiceRequestDO requestDO, InvoiceManageBatchImportADTO importADTO) {
        InvoiceRequestRecordDO recordDO = new InvoiceRequestRecordDO();
        recordDO.setManageId(manageDO.getId());
        recordDO.setBizNo(manageDO.getBizNo());
        recordDO.setRequestId(requestDO.getId());
        // 元转分 - requestDO.getAmount() 已经是转换后的分值
        recordDO.setAmount(requestDO.getAmount());
        recordDO.setStatus(InvoiceRequestStatusEnum.COMPLETED_INVOICE.getCode());
        recordDO.setFinanceFeedback("");
        recordDO.init();
        return recordDO;
    }

    private List<InvoiceRequestRecordDetailDO> buildInvoiceRequestRecordDetailDO(InvoiceRequestRecordDO requestRecordDO, List<InvoiceManageDetailDO> manageDetailDOList) {
        return manageDetailDOList.stream().map(item -> {
            InvoiceRequestRecordDetailDO detailDO = new InvoiceRequestRecordDetailDO();
            detailDO.setManageId(requestRecordDO.getManageId());
            detailDO.setManageDetailId(item.getId());
            detailDO.setRequestRecordId(requestRecordDO.getId());
            detailDO.setProductCode(item.getProductCode());
            detailDO.setProductCount(item.getProductCount());
            detailDO.setAmount(item.getExpectedInvoiceAmount());
            detailDO.setStatus(InvoiceRequestStatusEnum.COMPLETED_INVOICE.getCode());
            detailDO.init();
            return detailDO;
        }).collect(Collectors.toList());
    }

    private void handleImportFile(String fileUrl) {
        HttpResponse response = HttpRequest.get(fileUrl).timeout(60000).executeAsync();
        InputStream excelStream = response.bodyStream();
        Map<String, List<InvoiceManageBatchImportADTO>> waitHandleMap = new HashMap<>();

        AngelExcel.readBySax(excelStream, InvoiceManageBatchImportADTO.class, new AngelBatchRowHandler<InvoiceManageBatchImportADTO>() {
            @Override
            public Map<String, String> initHeaderAliasMap() {
                return getBatchImportHeaderAliasMap();
            }

            @Override
            public void handleRow(List<InvoiceManageBatchImportADTO> importADTOList) {
                if (CollectionUtils.isEmpty(importADTOList)) {
                    return;
                }

                importADTOList.forEach(item -> {
                    List<InvoiceManageBatchImportADTO> waitHandleList = waitHandleMap.computeIfAbsent(item.getBizNo(), k -> new ArrayList<>());
                    waitHandleList.add(item);
                });
            }
        }, BATCH_NUM);

        waitHandleMap.forEach((key, value) -> {
            InvoiceManageBatchImportADTO importADTO = value.get(0);

            // 新增[应开明细]
            InvoiceManageDO manageDO = buildInvoiceManageDO(importADTO);
            invoiceManageDAO.insertSelective(manageDO);
            // 新增[应开产品明细]
            List<InvoiceManageDetailDO> manageDetailDOList = buildInvoiceManageDetailDO(manageDO.getId(), value);
            // 批量新增
            if (CollectionUtils.isNotEmpty(manageDetailDOList)) {
                invoiceManageDetailDAO.batchInsert(manageDetailDOList);
            }

            // 若应开金额>0，则新增应开变更记录和应开变更记录详情
            if (importADTO.getExpectedInvoiceAmount().compareTo(BigDecimal.ZERO) > 0) {
                // 新增[应开变更记录]
                InvoiceChangeRecordDO recordDO = buildInvoiceChangeRecordDO(manageDO.getId(), importADTO);
                invoiceChangeRecordDAO.insertSelective(recordDO);
                // 新增[应开变更记录详情]
                List<InvoiceChangeRecordDetailDO> recordDetailDOList = buildInvoiceChangeRecordDetailDO(manageDO.getId(), recordDO.getId(), manageDetailDOList);
                // 批量新增
                if (CollectionUtils.isNotEmpty(recordDetailDOList)) {
                    invoiceChangeRecordDetailDAO.batchInsert(recordDetailDOList);
                }
            }

            // 若已开金额>0，则新增开票申请、开票申请记录和开票申请记录明细
            if (importADTO.getCompletedInvoiceAmount().compareTo(BigDecimal.ZERO) > 0) {
                InvoiceRequestDO requestDO = buildInvoiceRequestDO(importADTO);
                invoiceRequestDAO.insertSelective(requestDO);

                InvoiceRequestRecordDO requestRecordDO = buildInvoiceRequestRecordDO(manageDO, requestDO, importADTO);
                invoiceRequestRecordDAO.insertSelective(requestRecordDO);

                List<InvoiceRequestRecordDetailDO> requestRecordDetailDOList = buildInvoiceRequestRecordDetailDO(requestRecordDO, manageDetailDOList);
                // 批量新增
                if (CollectionUtils.isNotEmpty(requestRecordDetailDOList)) {
                    invoiceRequestRecordDetailDAO.batchInsert(requestRecordDetailDOList);
                }
            }

        });

    }

    private void preValidFile(String fileUrl) {
        HttpResponse response = HttpRequest.get(fileUrl).timeout(60000).executeAsync();
        InputStream excelStream = response.bodyStream();

        AngelExcel.readBySax(excelStream, InvoiceManageBatchImportADTO.class, new AngelBatchRowHandler<InvoiceManageBatchImportADTO>() {
            @Override
            public Map<String, String> initHeaderAliasMap() {
                return getBatchImportHeaderAliasMap();
            }

            @Override
            public void handleRow(List<InvoiceManageBatchImportADTO> importADTOList) {
                if (CollectionUtils.isEmpty(importADTOList)) {
                    return;
                }

                importADTOList.forEach(item -> {
                    String result = validate(item);
                    if (StringUtils.isNotBlank(result)) {
                        String errMsg = String.format("导入失败，第%s行数据错误，错误原因是%s", item.getCurrRowIndex() + 1, result);
                        throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, errMsg);
                    }
                });
            }
        }, BATCH_NUM);
    }


    /**
     * 执行校验
     */
    private void validateBatchImportData(List<InvoiceManageBatchImportDTO> dataList) throws Exception {
        // 2.1 业务单号要跟系统中已有的应开明细的业务单号不重复
        Set<String> bizNoSet = dataList.stream().map(InvoiceManageBatchImportDTO::getBizNo).collect(Collectors.toSet());
        for (String bizNo : bizNoSet) {
            if (invoiceManageManager.getByBizNoAndBizType(bizNo, 1) != null) {
                throw new Exception("业务单号" + bizNo + "已存在，不能重复导入");
            }
        }

        // 2.2 渠道商ID需校验系统中是否存在
        Set<Long> subjectIdSet = dataList.stream().map(InvoiceManageBatchImportDTO::getSubjectId).collect(Collectors.toSet());
        for (Long subjectId : subjectIdSet) {
            AgentDTO agentDTO = agentManager.findById(subjectId.intValue());
            if (agentDTO == null) {
                throw new Exception("渠道商ID" + subjectId + "不存在");
            }
        }

        // 2.3 待开票总金额需=应开票总金额-开票中总金额-已开票总金额
        // 2.4 待开票金额需=应开票金额-开票中金额-已开票金额
        for (InvoiceManageBatchImportDTO data : dataList) {
            // 校验总金额
            long expectedPendingTotal = data.getExpectedInvoiceAmountCent() - data.getOngoingInvoiceAmountCent() - data.getCompletedInvoiceAmountCent();
            if (!data.getPendingInvoiceAmountCent().equals(expectedPendingTotal)) {
                throw new Exception("业务单号" + data.getBizNo() + "的待开票总金额计算错误");
            }

            // 校验明细金额
            long expectedPendingDetail = data.getExpectedAmountCent() - data.getOngoingAmountCent() - data.getCompletedAmountCent();
            if (!data.getPendingAmountCent().equals(expectedPendingDetail)) {
                throw new Exception("业务单号" + data.getBizNo() + "的待开票金额计算错误");
            }
        }
    }

    /**
     * 数据校验
     */
    private String validate(InvoiceManageBatchImportADTO rowData) {
        StringBuilder sb = new StringBuilder();

        // 1.1 所有字段不能为空
        if (StringUtils.isBlank(rowData.getBizNo())) {
            sb.append("业务单号不能为空；");
        }
        if (rowData.getBizType() == null) {
            sb.append("业务类型不能为空；");
        }
        if (rowData.getSubjectType() == null) {
            sb.append("渠道商类型不能为空；");
        }
        if (rowData.getSubjectId() == null) {
            sb.append("渠道商ID不能为空；");
        }
        if (StringUtils.isBlank(rowData.getSubjectName())) {
            sb.append("渠道商名称不能为空；");
        }
        if (rowData.getTotalAmount() == null) {
            sb.append("业务单总金额不能为空；");
        }
        if (rowData.getExpectedInvoiceAmount() == null) {
            sb.append("应开票总金额不能为空；");
        }
        if (rowData.getOngoingInvoiceAmount() == null) {
            sb.append("开票中总金额不能为空；");
        }
        if (rowData.getCompletedInvoiceAmount() == null) {
            sb.append("已开票总金额不能为空；");
        }
        if (rowData.getPendingInvoiceAmount() == null) {
            sb.append("待开票总金额不能为空；");
        }
        if (StringUtils.isBlank(rowData.getBizCreateTime())) {
            sb.append("业务单创建时间不能为空；");
        }
        if (StringUtils.isBlank(rowData.getPaymentTime())) {
            sb.append("业务单付款完成时间不能为空；");
        }
        if (StringUtils.isBlank(rowData.getProductCode())) {
            sb.append("产品名称不能为空；");
        }
        if (rowData.getProductCount() == null) {
            sb.append("产品数量不能为空；");
        }
        if (rowData.getAmount() == null) {
            sb.append("业务单金额不能为空；");
        }
        if (rowData.getExpectedAmount() == null) {
            sb.append("应开票金额不能为空；");
        }
        if (rowData.getOngoingAmount() == null) {
            sb.append("开票中金额不能为空；");
        }
        if (rowData.getCompletedAmount() == null) {
            sb.append("已开票金额不能为空；");
        }
        if (rowData.getPendingAmount() == null) {
            sb.append("待开票金额不能为空；");
        }

        // 1.2 业务类型必须是1（设备采购）
        if (rowData.getBizType() != null && !rowData.getBizType().equals(1)) {
            sb.append("业务类型必须是1（设备采购）；");
        }

        // 1.3 渠道商类型必须是0或5（代理商或合资公司）
        if (rowData.getSubjectType() != null &&
                !rowData.getSubjectType().equals(0) && !rowData.getSubjectType().equals(5)) {
            sb.append("渠道商类型必须是0（代理商）或5（合资公司）；");
        }

        // 1.4 金额类字段必须是数字格式，不可为负数，最多保留两位小数
        if (!validateAmount(rowData.getTotalAmount())) {
            sb.append("业务单总金额格式错误；");
        }
        if (!validateAmount(rowData.getExpectedInvoiceAmount())) {
            sb.append("应开票总金额格式错误；");
        }
        if (!validateAmount(rowData.getOngoingInvoiceAmount())) {
            sb.append("开票中总金额格式错误；");
        }
        if (!validateAmount(rowData.getCompletedInvoiceAmount())) {
            sb.append("已开票总金额格式错误；");
        }
        if (!validateAmount(rowData.getPendingInvoiceAmount())) {
            sb.append("待开票总金额格式错误；");
        }
        if (!validateAmount(rowData.getAmount())) {
            sb.append("业务单金额格式错误；");
        }
        if (!validateAmount(rowData.getExpectedAmount())) {
            sb.append("应开票金额格式错误；");
        }
        if (!validateAmount(rowData.getOngoingAmount())) {
            sb.append("开票中金额格式错误；");
        }
        if (!validateAmount(rowData.getCompletedAmount())) {
            sb.append("已开票金额格式错误；");
        }
        if (!validateAmount(rowData.getPendingAmount())) {
            sb.append("待开票金额格式错误；");
        }

        // 1.5 业务单创建时间和业务单付款时间：必须为时间格式，精确到秒
        if (!validateDateTime(rowData.getBizCreateTime())) {
            sb.append("业务单创建时间格式错误，应为yyyy-MM-dd HH:mm:ss；");
        }
        if (!validateDateTime(rowData.getPaymentTime())) {
            sb.append("业务单付款完成时间格式错误，应为yyyy-MM-dd HH:mm:ss；");
        }

        // 1.6 产品数量必须为非负整数
        if (rowData.getProductCount() != null && rowData.getProductCount() < 0) {
            sb.append("产品数量必须为非负整数；");
        }

        InvoiceManageDTO invoiceManageDTO = invoiceManageManager.getByBizNoAndBizType(rowData.getBizNo(), BizTypeEnum.PURCHASE_ORDER.getCode());
        if (invoiceManageDTO != null) {
            sb.append("此业务单号对应的应开明细已存在；");
        }

        AgentDTO agentDTO = agentManager.findById(rowData.getSubjectId().intValue());
        if (agentDTO == null) {
            sb.append("渠道商信息不存在；");
        }

        // 待开票总金额：需=应开票总金额-开票中总金额-已开票总金额
        BigDecimal expectedPendingTotalAmount = rowData.getExpectedInvoiceAmount()
                .subtract(rowData.getOngoingInvoiceAmount())
                .subtract(rowData.getCompletedInvoiceAmount());
        if (rowData.getPendingInvoiceAmount().compareTo(expectedPendingTotalAmount) != 0) {
            sb.append("待开票总金额计算错误，应为").append(expectedPendingTotalAmount).append("；");
        }

        // 待开票金额：需=应开票金额-开票中金额-已开票金额
        BigDecimal expectedPendingAmount = rowData.getExpectedAmount()
                .subtract(rowData.getOngoingAmount())
                .subtract(rowData.getCompletedAmount());
        if (rowData.getPendingAmount().compareTo(expectedPendingAmount) != 0) {
            sb.append("待开票金额计算错误，应为").append(expectedPendingAmount).append("；");
        }

        return sb.toString();
    }

    /**
     * 校验金额格式
     */
    private boolean validateAmount(BigDecimal amount) {
        if (amount == null) {
            return false;
        }
        // 不能为负数
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            return false;
        }
        // 最多保留两位小数
        return amount.scale() <= 2;
    }

    /**
     * 校验时间格式
     */
    private boolean validateDateTime(String dateTime) {
        if (StringUtils.isBlank(dateTime)) {
            return false;
        }
        try {
            DATE_FORMAT.parse(dateTime);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

}