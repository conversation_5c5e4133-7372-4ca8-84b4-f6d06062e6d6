package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceChangeRecordDetailDO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【invoice_change_record_detail(应开变更记录详情表)】的数据库操作Mapper
 * @createDate 2025-03-06 14:25:21
 * @Entity so.dian.invoice.pojo.entity.InvoiceChangeRecord
 */
public interface InvoiceChangeRecordDetailDAO {

    int deleteByPrimaryKey(Long id);

    int insert(InvoiceChangeRecordDetailDO record);

    int insertSelective(InvoiceChangeRecordDetailDO record);

    InvoiceChangeRecordDetailDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InvoiceChangeRecordDetailDO record);

    int updateByPrimaryKey(InvoiceChangeRecordDetailDO record);

    List<InvoiceChangeRecordDetailDO> getByManageId(Long manageId);

    int batchInsert(@Param("list") List<InvoiceChangeRecordDetailDO> list);

}
