package so.dian.invoice.dao;

import org.apache.ibatis.annotations.Param;
import so.dian.invoice.pojo.entity.InvoiceManageDetailDO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【invoice_manage_detail(开票管理详情表)】的数据库操作Mapper
 * @createDate 2025-03-06 14:25:21
 * @Entity so.dian.invoice.pojo.entity.InvoiceChangeRecord
 */
public interface InvoiceManageDetailDAO {

    int deleteByPrimaryKey(Long id);

    int insert(InvoiceManageDetailDO record);

    int insertSelective(InvoiceManageDetailDO record);

    InvoiceManageDetailDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InvoiceManageDetailDO record);

    int updateByPrimaryKey(InvoiceManageDetailDO record);

    List<InvoiceManageDetailDO> listByManageIds(@Param("manageIds") List<Long> manageIds);

    List<InvoiceManageDetailDO> getByManageId(@Param("manageId")Long manageId);

    int batchInsert(@Param("list") List<InvoiceManageDetailDO> list);
}
